#!/bin/sh


# MmeActionInsId Mme{ImagePath} {MmeDate}Id

# SrcDBName,ColSep
#

# 参数验证和默认值设置
MmeActionInsId="${1:-}"
MmeImagePath="${2:-}"
MmeDateId="${3:-}"
SrcDBName="${4:-}"
ColSep="${5:-}"

# 参数验证
if [ -z "$MmeImagePath" ]; then
    echo "错误: MmeImagePath 参数不能为空"
    exit 1
fi

if [ -z "$MmeDateId" ]; then
    echo "错误: MmeDateId 参数不能为空"
    exit 1
fi

if [ -z "$SrcDBName" ]; then
    echo "错误: SrcDBName 参数不能为空，请提供数据库名称"
    exit 1
fi

ImagePath=$(cd "$MmeImagePath" && pwd)

LogPath=${ImagePath}/log
if [ ! -d ${LogPath} ]; then
    mkdir ${LogPath}
fi
LogName=${LogPath}/../../user

MmeDate="${MmeDateId:0:8}"

BeforeDay=$(date +"%Y%m%d" -d"${MmeDate} -1day")

starttime=${BeforeDay}"000000"
endtime=${MmeDate}"000000"

if [ "" == "$ColSep" ]; then
        ColSep="|"
fi
TmpFileName="a_10000_${BeforeDay}_VGOP1-R2.10-24201.unl"
nowdate=$(date +"%Y%m%d%H%M%S")
# 数据库查询，添加错误处理
revtimes=$(echo "select * from bms_vgop_revtimes where datatime='${BeforeDay}' and tmpfilename='${TmpFileName}'" | dbaccess ${SrcDBName} 2>>${LogName} | grep 'times' | awk -F" " '{print$2}')

# 安全的条件判断，处理空值情况
if [ -n "${revtimes}" ] && [ "${revtimes}" != "" ]; then
	revtimes=$(expr ${revtimes} + 1)
	if [ $(expr length ${revtimes}) -eq 1 ]; then
		revtimes="0"${revtimes}
	fi
	echo "update bms_vgop_revtimes set times=${revtimes} where datatime='${BeforeDay}' and tmpfilename='${TmpFileName}'" | dbaccess ${SrcDBName} 1>>${LogName} 2>&1
else
	revtimes="00"
	echo "insert into bms_vgop_revtimes(datatime,times,tmpfilename,optime) values('${BeforeDay}','${revtimes}','${TmpFileName}','${nowdate}')" | dbaccess ${SrcDBName} 1>>${LogName} 2>&1
fi

daydatapath=${ImagePath}"/VGOPdata/datafile/"${BeforeDay}"/day/"
UnloadFileName=${daydatapath}${TmpFileName}

if [ ! -d ${daydatapath} ]; then
    mkdir -p ${daydatapath}  1>>${LogName} 2>&1
fi

echo "[$MmeActionInsId] unloadFileName="${UnloadFileName}  >>${LogName}

sql="select mum.phonenumber as phonenumber, mum.phonestate as phonestate ,mum.phoneimsi as phoneimsi,mum.phoneimei as phoneimei,mum.locationid as locationid,substr(bp.bossid, 1, 3) as provinceid,
mum.openingtime as openingtime,mum.Optime as Optime,mum.sex as sex from mcn_user_major mum left join bossprovince bp on mum.provinceid = bp.provinceid
where mum.openingtime>='${starttime}' and mum.openingtime<'${endtime}' and mum.phonestate in ('0','1')"

UnloadCmd="set lock mode to wait 10;unload to ${UnloadFileName} delimiter '$ColSep' ${sql};"

echo "[$MmeActionInsId]"$(date +"%Y%m%d %H%M%S:")"Unload Start: ${UnloadCmd}" >>${LogName}

echo "${UnloadCmd}" | dbaccess $SrcDBName  1>>${LogName} 2>&1

echo "[$MmeActionInsId]"$(date +"%Y%m%d %H%M%S:")"Unload END." >>${LogName}

linenum=$(wc -l ${UnloadFileName} | awk '{print $1}')
if [ ${linenum} -eq 0 ];then
	linenum=1
fi
filenum=001
n1=1
JYFileName=${daydatapath}"a_10000_${BeforeDay}_VGOP1-R2.10-24201_${revtimes}.verf"
while [ ${n1} -le ${linenum} ]
do 
	n2=$(expr ${n1} + 2000000) 
	FileName=${daydatapath}"a_10000_${BeforeDay}_VGOP1-R2.10-24201_${revtimes}_${filenum}.dat"
	sed -n "${n1},${n2}p" ${UnloadFileName} | nl -s '|' | sed 's/|$//' | tr -d '\' | tr -d ' ' | tr '|' '\200' | awk '{print($0"\r")}' >${FileName}	
	echo "$(basename ${FileName}) $(wc -c ${FileName}) $(wc -l ${FileName}) ${BeforeDay} ${nowdate}" | awk '{print($1"|"$2"|"$4"|"$6"|"$7)}' | tr '|' '\200' | awk '{print($0"\r")}' >>${JYFileName}
	n1=$(expr ${n2} + 1)
	filenum=$(expr ${filenum} + 1)
	filenumlength=$(echo ${filenum} | awk '{print length($0)}')
	if [ ${filenumlength} -eq 1 ];then
		filenum="00"${filenum}
	elif [ ${filenumlength} -eq 2 ];then
		filenum="0"${filenum}
	fi
done
